@import 'tailwindcss';

/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
/* Monument Extended will be loaded as a fallback to Inter for now, as it's a premium font */

/* Hitez Brand Theme - Light Mode */
:root {
	--color-primary: #f8f9fa; /* Off-white background */
	--color-accent: #00ffff; /* Electric Blue */
	--color-accent-secondary: #00cccc;
	--color-accent-tertiary: #0099cc;
	--color-text: #1a1a1a; /* Dark charcoal */
	--color-text-muted: #6b7280;
	--color-light: #ffffff;
	--color-lighter: #f8f9fa;
	--color-surface: #ffffff;
	--color-surface-elevated: #f3f4f6;
	--color-border: #e5e7eb;
	--color-border-light: #f3f4f6;
	--font-heading: 'Monument Extended', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
	--font-body: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
	--font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
	--border-radius: 12px;
	--border-radius-lg: 20px;
	--shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
	--shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
	--shadow-large: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Base Styles */
* {
	box-sizing: border-box;
}

html {
	scroll-behavior: smooth;
	overflow-x: hidden;
}

/* Full Viewport Scrolling */
.fullpage-container {
	height: 100vh;
	overflow-y: scroll;
	scroll-snap-type: y mandatory;
	scroll-behavior: smooth;
}

.fullpage-section {
	height: 100vh;
	scroll-snap-align: start;
	scroll-snap-stop: always;
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
}

/* Mobile viewport fix */
@supports (-webkit-touch-callout: none) {
	.fullpage-section {
		height: 100vh;
		height: -webkit-fill-available;
	}

	.fullpage-container {
		height: 100vh;
		height: -webkit-fill-available;
	}
}

/* Disable scroll bouncing on iOS */
.fullpage-container {
	-webkit-overflow-scrolling: touch;
	overscroll-behavior: none;
}

/* Performance optimizations */
.fullpage-section * {
	will-change: transform, opacity;
}

/* Smooth scrolling fallback for older browsers */
@media (prefers-reduced-motion: no-preference) {
	.fullpage-container {
		scroll-behavior: smooth;
	}
}

@media (prefers-reduced-motion: reduce) {
	.fullpage-container {
		scroll-behavior: auto;
	}

	/* GSAP will handle reduced motion automatically */
}

body {
	font-family: var(--font-body);
	background: var(--color-primary);
	color: var(--color-text);
	line-height: 1.6;
	overflow-x: hidden;
	font-feature-settings:
		'liga' 1,
		'kern' 1;
	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/* Modern Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
	font-family: var(--font-heading);
	letter-spacing: -0.02em;
	text-rendering: optimizeLegibility;
}

/* Hero Heading - Extra Large */
h1 {
	font-size: clamp(3rem, 8vw, 6rem);
	font-weight: 800;
	line-height: 0.95;
	letter-spacing: -0.04em;
}

/* Section Headings - Large */
h2 {
	font-size: clamp(2.25rem, 6vw, 4rem);
	font-weight: 700;
	line-height: 1.1;
	letter-spacing: -0.03em;
}

/* Subsection Headings - Medium */
h3 {
	font-size: clamp(1.25rem, 3vw, 1.75rem);
	font-weight: 600;
	line-height: 1.3;
	letter-spacing: -0.01em;
}

/* Small Headings */
h4 {
	font-size: clamp(1rem, 2vw, 1.25rem);
	font-weight: 600;
	line-height: 1.4;
}

/* Modern Paragraph Styles */
p {
	line-height: 1.6;
	font-weight: 400;
}

/* Subtitle/Lead Text */
.text-lead {
	font-size: clamp(1.125rem, 2.5vw, 1.5rem);
	line-height: 1.5;
	font-weight: 400;
	letter-spacing: -0.01em;
}

/* Caption/Small Text */
.text-caption {
	font-size: clamp(0.875rem, 1.5vw, 1rem);
	line-height: 1.5;
	font-weight: 500;
	letter-spacing: 0.01em;
	text-transform: uppercase;
}

/* Custom Utility Classes */
.text-accent {
	color: var(--color-accent);
}

.bg-primary {
	background-color: var(--color-primary);
}

.bg-dark {
	background-color: var(--color-dark);
}

.bg-darker {
	background-color: var(--color-darker);
}

.border-accent {
	border-color: var(--color-accent);
}

/* Button Styles */
.btn {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	padding: 0.875rem 2.5rem;
	font-family: var(--font-heading);
	font-weight: 600;
	text-decoration: none;
	border-radius: var(--border-radius);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	cursor: pointer;
	border: none;
	font-size: 1rem;
	letter-spacing: -0.02em;
	position: relative;
	overflow: hidden;
	backdrop-filter: blur(10px);
}

.btn-primary {
	background: var(--color-accent);
	color: var(--color-light);
	font-weight: 700;
}

.btn-primary:hover {
	background: #1d4ed8;
	transform: translateY(-2px);
}

.btn-outline {
	background: var(--color-surface);
	color: var(--color-accent);
	border: 2px solid var(--color-accent);
}

.btn-outline:hover {
	background: var(--color-accent);
	color: var(--color-light);
	transform: translateY(-2px);
}

.btn-ghost {
	background: var(--color-surface);
	color: var(--color-accent);
	border: 1px solid var(--color-border);
}

.btn-ghost:hover {
	background: var(--color-surface-elevated);
	transform: translateY(-1px);
}

/* GSAP Animation Base Styles */
.gsap-section {
	position: relative;
	overflow: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
	width: 8px;
}

::-webkit-scrollbar-track {
	background: var(--color-dark);
}

::-webkit-scrollbar-thumb {
	background: var(--color-accent);
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: rgba(0, 255, 255, 0.8);
}

/* Selection */
::selection {
	background-color: var(--color-accent);
	color: var(--color-dark);
}

/* Enhanced Animations */
@keyframes float {
	0%,
	100% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-20px);
	}
}

/* GSAP Animation Preparation */
.gsap-fade-in {
	opacity: 0;
}

.gsap-slide-up {
	opacity: 0;
	transform: translateY(50px);
}

.gsap-slide-left {
	opacity: 0;
	transform: translateX(-50px);
}

.gsap-slide-right {
	opacity: 0;
	transform: translateX(50px);
}

.gsap-scale-in {
	opacity: 0;
	transform: scale(0.8);
}

/* Z Logo Styles */
.z-logo {
	font-family: var(--font-heading);
	font-weight: 800;
	font-style: italic;
	position: relative;
	display: inline-block;
}

.z-logo::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, var(--color-accent), transparent);
	opacity: 0;
	pointer-events: none;
}

/* GSAP Performance Optimizations */
.will-change-transform {
	will-change: transform;
}

.will-change-opacity {
	will-change: opacity;
}

.will-change-auto {
	will-change: auto;
}

/* Stagger animation delays */
.stagger-1 {
	animation-delay: 0.1s;
}
.stagger-2 {
	animation-delay: 0.2s;
}
.stagger-3 {
	animation-delay: 0.3s;
}
.stagger-4 {
	animation-delay: 0.4s;
}

.hover-scale:hover {
	transform: scale(1.05);
}

.hover-rotate:hover {
	transform: rotate(5deg);
}

/* Gradient text effect */
.gradient-text {
	background: linear-gradient(45deg, var(--color-accent), #ffffff);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

/* Glitch effect */
.glitch {
	position: relative;
}

.glitch::before,
.glitch::after {
	content: attr(data-text);
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.glitch::before {
	animation: glitch-1 0.5s infinite;
	color: #ff00ff;
	z-index: -1;
}

.glitch::after {
	animation: glitch-2 0.5s infinite;
	color: #00ffff;
	z-index: -2;
}

@keyframes glitch-1 {
	0%,
	14%,
	15%,
	49%,
	50%,
	99%,
	100% {
		transform: translate(0);
	}
	15%,
	49% {
		transform: translate(-2px, -1px);
	}
}

@keyframes glitch-2 {
	0%,
	20%,
	21%,
	62%,
	63%,
	99%,
	100% {
		transform: translate(0);
	}
	21%,
	62% {
		transform: translate(2px, 1px);
	}
}

/* Professional Utility Classes */
.accent-text {
	color: var(--color-accent);
}

.accent-border {
	background: var(--color-surface);
	border: 2px solid var(--color-accent);
	border-radius: var(--border-radius);
}

.glass-effect {
	background: rgba(255, 255, 255, 0.8);
	backdrop-filter: blur(20px);
	border: 1px solid var(--color-border-light);
	border-radius: var(--border-radius);
}

.text-shadow-glow {
	text-shadow: 0 0 20px rgba(0, 245, 255, 0.5);
}

.border-accent {
	border: 1px solid var(--color-border);
	border-radius: var(--border-radius);
}

.border-accent:hover {
	border-color: var(--color-accent);
}

/* Enhanced Visual Hierarchy */
.section-padding {
	padding: 5rem 1.5rem;
}

.section-padding-lg {
	padding: 8rem 1.5rem;
}

.container-narrow {
	max-width: 800px;
	margin: 0 auto;
}

.container-wide {
	max-width: 1400px;
	margin: 0 auto;
}

.grid-auto-fit {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
	gap: 2rem;
}

.grid-auto-fill {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
	gap: 1.5rem;
}

/* Enhanced Responsive Typography */
@media (max-width: 768px) {
	.btn {
		padding: 0.75rem 2rem;
		font-size: 0.9rem;
	}

	/* Mobile heading adjustments */
	.heading-display {
		font-size: clamp(2.5rem, 12vw, 4rem);
		line-height: 0.95;
	}

	.heading-hero {
		font-size: clamp(2rem, 10vw, 3.5rem);
		line-height: 1;
	}

	.heading-section {
		font-size: clamp(1.75rem, 8vw, 2.5rem);
		line-height: 1.1;
	}



	.text-xl-lead {
		font-size: clamp(1.125rem, 4vw, 1.375rem);
	}

	.text-lg-lead {
		font-size: clamp(1rem, 3vw, 1.25rem);
	}

	.section-padding {
		padding: 3rem 1rem;
	}

	.section-padding-lg {
		padding: 4rem 1rem;
	}

	/* Improved mobile spacing */
	.heading-spacing {
		margin-bottom: clamp(0.75rem, 2vw, 1.5rem);
	}

	.section-spacing {
		margin-bottom: clamp(1.5rem, 4vw, 3rem);
	}
}

/* Section Navigation */
.section-nav {
	position: fixed;
	right: 2rem;
	top: 50%;
	transform: translateY(-50%);
	z-index: 1000;
	display: flex;
	flex-direction: column;
	gap: 1rem;
}

.section-nav-dot {
	width: 12px;
	height: 12px;
	border-radius: 50%;
	background: var(--color-border);
	border: 2px solid var(--color-surface);
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
}

.section-nav-dot:hover {
	background: var(--color-accent);
	transform: scale(1.2);
}

.section-nav-dot.active {
	background: var(--color-accent);
	transform: scale(1.3);
	box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.2);
}

.section-nav-dot::after {
	content: attr(data-label);
	position: absolute;
	right: 120%;
	top: 50%;
	transform: translateY(-50%);
	background: var(--color-surface);
	color: var(--color-text);
	padding: 0.5rem 1rem;
	border-radius: var(--border-radius);
	font-size: 0.875rem;
	font-weight: 500;
	white-space: nowrap;
	opacity: 0;
	pointer-events: none;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	box-shadow: var(--shadow-medium);
	border: 1px solid var(--color-border);
}

.section-nav-dot:hover::after {
	opacity: 1;
	transform: translateY(-50%) translateX(-8px);
}

/* Mobile section navigation */
@media (max-width: 768px) {
	.section-nav {
		right: 1rem;
		gap: 0.75rem;
	}

	.section-nav-dot {
		width: 10px;
		height: 10px;
	}

	.section-nav-dot::after {
		display: none;
	}
}

/* GSAP Section Styles */
.hero-section {
	height: 100vh;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.philosophy-section,
.services-section,
.team-section,
.cta-section {
	min-height: 100vh;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

/* Modern Heading Utilities */
.heading-display {
	font-size: clamp(3.5rem, 10vw, 7rem);
	font-weight: 800;
	line-height: 0.9;
	letter-spacing: -0.05em;
}

.heading-hero {
	font-size: clamp(2.5rem, 7vw, 5rem);
	font-weight: 700;
	line-height: 1;
	letter-spacing: -0.03em;
}

.heading-section {
	font-size: clamp(2rem, 5vw, 3.5rem);
	font-weight: 600;
	line-height: 1.1;
	letter-spacing: -0.02em;
}

.heading-subsection {
	font-size: clamp(1.125rem, 2vw, 1.375rem);
	font-weight: 600;
	line-height: 1.2;
	letter-spacing: -0.01em;
}

/* Text Hierarchy */
.text-xl-lead {
	font-size: clamp(1.25rem, 3vw, 2rem);
	line-height: 1.4;
	font-weight: 400;
	letter-spacing: -0.01em;
}

.text-lg-lead {
	font-size: clamp(1.125rem, 2.5vw, 1.5rem);
	line-height: 1.5;
	font-weight: 400;
}

.text-balanced {
	text-wrap: balance;
}

/* Accent Text Variations */
.accent-text {
	color: var(--color-accent);
}

.accent-text-gradient {
	background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-secondary) 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

/* Spacing Utilities */
.heading-spacing {
	margin-bottom: clamp(1rem, 3vw, 2rem);
}

.section-spacing {
	margin-bottom: clamp(2rem, 5vw, 4rem);
}

/* Default cursor behavior restored */
