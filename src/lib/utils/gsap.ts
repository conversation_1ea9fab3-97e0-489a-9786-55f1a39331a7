import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { browser } from '$app/environment';

// Register ScrollTrigger plugin
if (browser) {
	gsap.registerPlugin(ScrollTrigger);
}

// GSAP Animation Utilities for Hitez
export class GSAPAnimations {
	private static initialized = false;

	static init() {
		if (!browser || this.initialized) return;
		
		// Set GSAP defaults
		gsap.defaults({
			duration: 1,
			ease: 'power2.out'
		});

		// Handle reduced motion preference
		if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
			gsap.globalTimeline.timeScale(0.01);
		}

		this.initialized = true;
	}

	// Hero Section Initial Load Animation
	static heroInitialAnimation() {
		if (!browser) return;

		const tl = gsap.timeline();

		// Logo reveal with trail effect
		tl.fromTo('.z-logo', 
			{ 
				x: -100, 
				opacity: 0,
				scale: 0.8
			}, 
			{ 
				x: 0, 
				opacity: 1,
				scale: 1,
				duration: 1.2,
				ease: 'power3.out'
			}
		)
		// Trail effect
		.to('.z-logo::after', {
			opacity: 1,
			duration: 0.3,
			ease: 'power2.in'
		}, '-=0.8')
		.to('.z-logo::after', {
			opacity: 0,
			duration: 0.5,
			ease: 'power2.out'
		}, '-=0.2')
		// Slogan stagger reveal
		.fromTo('.hero-word', 
			{ 
				y: 50, 
				opacity: 0,
				rotationX: 90
			}, 
			{ 
				y: 0, 
				opacity: 1,
				rotationX: 0,
				duration: 0.8,
				stagger: 0.15,
				ease: 'back.out(1.7)'
			}, '-=0.5'
		)
		// Subtitle and CTA fade in
		.fromTo('.hero-subtitle, .hero-cta', 
			{ 
				y: 30, 
				opacity: 0 
			}, 
			{ 
				y: 0, 
				opacity: 1,
				duration: 0.8,
				stagger: 0.1,
				ease: 'power2.out'
			}, '-=0.3'
		);

		return tl;
	}

	// Philosophy Section Animation
	static philosophyAnimation(trigger: string) {
		if (!browser) return;

		return ScrollTrigger.create({
			trigger,
			start: 'top 80%',
			end: 'bottom 20%',
			animation: gsap.timeline()
				// Headline with masking effect
				.fromTo(`${trigger} .philosophy-headline`, 
					{ 
						y: 50, 
						opacity: 0,
						clipPath: 'inset(100% 0 0 0)'
					}, 
					{ 
						y: 0, 
						opacity: 1,
						clipPath: 'inset(0% 0 0 0)',
						duration: 1,
						ease: 'power3.out'
					}
				)
				// Text blocks stagger
				.fromTo(`${trigger} .philosophy-text`, 
					{ 
						y: 30, 
						opacity: 0 
					}, 
					{ 
						y: 0, 
						opacity: 1,
						duration: 0.8,
						stagger: 0.1,
						ease: 'power2.out'
					}, '-=0.5'
				)
				// Background element scrub effect
				.fromTo(`${trigger} .philosophy-bg`, 
					{ 
						rotation: 0,
						opacity: 0.1
					}, 
					{ 
						rotation: 360,
						opacity: 0.3,
						duration: 2,
						ease: 'none'
					}, 0
				),
			scrub: 1
		});
	}

	// Services Section Animation
	static servicesAnimation(trigger: string) {
		if (!browser) return;

		return ScrollTrigger.create({
			trigger,
			start: 'top 70%',
			animation: gsap.timeline()
				.fromTo(`${trigger} .services-headline`, 
					{ 
						y: 50, 
						opacity: 0 
					}, 
					{ 
						y: 0, 
						opacity: 1,
						duration: 1,
						ease: 'power3.out'
					}
				)
				.fromTo(`${trigger} .service-item`, 
					{ 
						y: 60, 
						opacity: 0,
						scale: 0.8
					}, 
					{ 
						y: 0, 
						opacity: 1,
						scale: 1,
						duration: 0.8,
						stagger: 0.15,
						ease: 'back.out(1.7)'
					}, '-=0.5'
				)
		});
	}

	// Service Item Hover Animation
	static serviceHoverAnimation(element: HTMLElement) {
		if (!browser) return;

		const icon = element.querySelector('.service-icon');
		
		element.addEventListener('mouseenter', () => {
			gsap.to(icon, {
				scale: 1.1,
				rotation: 5,
				duration: 0.3,
				ease: 'back.out(1.7)'
			});
		});

		element.addEventListener('mouseleave', () => {
			gsap.to(icon, {
				scale: 1,
				rotation: 0,
				duration: 0.3,
				ease: 'power2.out'
			});
		});
	}

	// Team Section with Pinning
	static teamAnimation(trigger: string) {
		if (!browser) return;

		const teamMembers = document.querySelectorAll(`${trigger} .team-member`);
		const progressDots = document.querySelectorAll(`${trigger} .progress-dot`);

		if (teamMembers.length === 0) return;

		// Create main timeline
		const mainTimeline = gsap.timeline();

		// Set initial states
		mainTimeline.set(teamMembers, { opacity: 0, y: 50 });
		mainTimeline.set(progressDots, { scale: 1, backgroundColor: 'var(--color-border)' });

		// Create sequence for each team member
		teamMembers.forEach((member, index) => {
			const isLast = index === teamMembers.length - 1;

			// Member entrance
			mainTimeline
				.to(member, {
					opacity: 1,
					y: 0,
					duration: 1,
					ease: 'power2.out'
				})
				.to(progressDots[index], {
					scale: 1.2,
					backgroundColor: 'var(--color-accent)',
					duration: 0.3
				}, '<')
				// Hold the member visible
				.to({}, { duration: 1 });

			// Member exit (except for the last one)
			if (!isLast) {
				mainTimeline
					.to(member, {
						opacity: 0,
						y: -50,
						duration: 1,
						ease: 'power2.in'
					})
					.to(progressDots[index], {
						scale: 1,
						backgroundColor: 'var(--color-border)',
						duration: 0.3
					}, '<');
			}
		});

		return ScrollTrigger.create({
			trigger,
			start: 'center center',
			end: `+=${teamMembers.length * 150}%`,
			pin: true,
			scrub: 1,
			animation: mainTimeline,
			onUpdate: (self) => {
				// Update progress dots based on scroll progress
				const progress = self.progress;
				const currentMemberIndex = Math.floor(progress * teamMembers.length);

				progressDots.forEach((dot, index) => {
					if (index === currentMemberIndex) {
						dot.classList.add('active');
					} else {
						dot.classList.remove('active');
					}
				});
			}
		});
	}

	// CTA Section Animation
	static ctaAnimation(trigger: string) {
		if (!browser) return;

		return ScrollTrigger.create({
			trigger,
			start: 'top 80%',
			animation: gsap.timeline()
				.fromTo(`${trigger} .cta-headline`, 
					{ 
						scale: 0.8, 
						opacity: 0,
						y: 50
					}, 
					{ 
						scale: 1, 
						opacity: 1,
						y: 0,
						duration: 1.2,
						ease: 'back.out(1.7)'
					}
				)
				.fromTo(`${trigger} .cta-form input`, 
					{ 
						y: 30, 
						opacity: 0 
					}, 
					{ 
						y: 0, 
						opacity: 1,
						duration: 0.6,
						stagger: 0.1,
						ease: 'power2.out'
					}, '-=0.8'
				)
				.fromTo(`${trigger} .cta-button`, 
					{ 
						scale: 0.8, 
						opacity: 0 
					}, 
					{ 
						scale: 1, 
						opacity: 1,
						duration: 0.8,
						ease: 'back.out(1.7)'
					}, '-=0.4'
				)
		});
	}

	// Button Hover Wipe Animation
	static buttonWipeAnimation(button: HTMLElement) {
		if (!browser) return;

		const wipeElement = button.querySelector('.button-wipe') as HTMLElement;
		
		button.addEventListener('mouseenter', () => {
			gsap.fromTo(wipeElement, 
				{ 
					scaleX: 0,
					transformOrigin: 'left center'
				}, 
				{ 
					scaleX: 1,
					duration: 0.4,
					ease: 'power2.out'
				}
			);
		});

		button.addEventListener('mouseleave', () => {
			gsap.to(wipeElement, {
				scaleX: 0,
				transformOrigin: 'right center',
				duration: 0.4,
				ease: 'power2.out'
			});
		});
	}

	// Cleanup function
	static cleanup() {
		if (!browser) return;
		ScrollTrigger.getAll().forEach(trigger => trigger.kill());
	}

	// Refresh ScrollTrigger (useful for dynamic content)
	static refresh() {
		if (!browser) return;
		ScrollTrigger.refresh();
	}

	// Handle responsive animations
	static setupResponsiveAnimations() {
		if (!browser) return;

		// Create responsive context
		ScrollTrigger.matchMedia({
			// Desktop animations
			"(min-width: 1024px)": () => {
				// Full animations for desktop
				return {
					// Desktop-specific animations can be added here
				};
			},

			// Tablet animations
			"(min-width: 768px) and (max-width: 1023px)": () => {
				// Simplified animations for tablet
				return {
					// Tablet-specific animations
				};
			},

			// Mobile animations
			"(max-width: 767px)": () => {
				// Minimal animations for mobile
				gsap.set('.team-section', { height: 'auto' });
				return {
					// Mobile-specific animations
				};
			}
		});
	}
}
