<script lang="ts">
	interface Props {
		size?: 'sm' | 'md' | 'lg' | 'xl';
		animated?: boolean;
		class?: string;
	}

	let { size = 'lg', animated = true, class: className = '' }: Props = $props();

	const sizeClasses = {
		sm: 'text-4xl',
		md: 'text-6xl', 
		lg: 'text-8xl',
		xl: 'text-9xl'
	};
</script>

<div class="z-logo-container {className}">
	<div class="z-logo {sizeClasses[size]} {animated ? 'animated' : ''}">
		Z
		<div class="z-logo-trail"></div>
	</div>
</div>

<style>
	.z-logo-container {
		display: inline-block;
		position: relative;
	}

	.z-logo {
		font-family: var(--font-heading);
		font-weight: 800;
		font-style: italic;
		color: var(--color-text);
		position: relative;
		display: inline-block;
		line-height: 1;
		letter-spacing: -0.05em;
		transform-origin: center;
		/* Sharp, cut ends effect */
		-webkit-text-stroke: 2px var(--color-accent);
		text-stroke: 2px var(--color-accent);
		-webkit-text-fill-color: transparent;
		text-fill-color: transparent;
	}

	.z-logo.animated {
		opacity: 0;
		transform: translateX(-100px) scale(0.8);
	}

	.z-logo-trail {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(
			90deg, 
			transparent 0%, 
			var(--color-accent) 30%, 
			rgba(0, 255, 255, 0.8) 50%, 
			var(--color-accent) 70%, 
			transparent 100%
		);
		opacity: 0;
		pointer-events: none;
		transform: skewX(-15deg);
		filter: blur(1px);
		z-index: -1;
	}

	/* Hover effect for non-animated logos */
	.z-logo:not(.animated):hover {
		transform: scale(1.05) rotate(2deg);
		transition: transform 0.3s ease;
	}

	.z-logo:not(.animated):hover .z-logo-trail {
		opacity: 0.3;
		transition: opacity 0.3s ease;
	}

	/* Responsive adjustments */
	@media (max-width: 768px) {
		.z-logo {
			-webkit-text-stroke: 1px var(--color-accent);
			text-stroke: 1px var(--color-accent);
		}
	}

	/* High contrast mode support */
	@media (prefers-contrast: high) {
		.z-logo {
			-webkit-text-fill-color: var(--color-text);
			text-fill-color: var(--color-text);
			-webkit-text-stroke: none;
			text-stroke: none;
		}
	}

	/* Reduced motion support */
	@media (prefers-reduced-motion: reduce) {
		.z-logo,
		.z-logo-trail {
			transition: none !important;
			animation: none !important;
		}
		
		.z-logo:not(.animated):hover {
			transform: none;
		}
	}
</style>
