<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { GSAPAnimations } from '$lib/utils/gsap.js';
	import type { ScrollTrigger } from 'gsap/ScrollTrigger';

	interface Props {
		id: string;
		class?: string;
		animationType?: 'philosophy' | 'services' | 'team' | 'cta' | 'none';
		children: any;
	}

	let { id, class: className = '', animationType = 'none', children }: Props = $props();

	let sectionElement: HTMLElement;
	let scrollTrigger: ScrollTrigger | undefined;

	onMount(() => {
		GSAPAnimations.init();

		if (animationType !== 'none' && sectionElement) {
			// Add a small delay to ensure D<PERSON> is fully rendered
			setTimeout(() => {
				switch (animationType) {
					case 'philosophy':
						scrollTrigger = GSAPAnimations.philosophyAnimation(`#${id}`);
						break;
					case 'services':
						scrollTrigger = GSAPAnimations.servicesAnimation(`#${id}`);
						break;
					case 'team':
						scrollTrigger = GSAPAnimations.teamAnimation(`#${id}`);
						break;
					case 'cta':
						scrollTrigger = GSAPAnimations.ctaAnimation(`#${id}`);
						break;
				}
			}, 100);
		}
	});

	onDestroy(() => {
		if (scrollTrigger) {
			scrollTrigger.kill();
		}
	});
</script>

<section 
	bind:this={sectionElement}
	{id} 
	class="gsap-section {className}"
>
	{@render children()}
</section>

<style>
	.gsap-section {
		position: relative;
		overflow: hidden;
		min-height: 100vh;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* Ensure proper stacking context for animations */
	.gsap-section :global(*) {
		position: relative;
		z-index: 1;
	}

	/* Background elements should be behind content */
	.gsap-section :global(.philosophy-bg),
	.gsap-section :global(.section-bg) {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
		pointer-events: none;
	}
</style>
