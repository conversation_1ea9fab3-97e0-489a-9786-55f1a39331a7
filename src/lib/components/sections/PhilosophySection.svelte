<script lang="ts">
	import GSAPSection from '$lib/components/GSAPSection.svelte';

	interface Props {
		content: {
			title: string;
			subtitle: string;
			textBlocks: Array<{ text: string }>;
		};
	}

	let { content }: Props = $props();
</script>

<GSAPSection id="philosophy" animationType="philosophy" class="philosophy-wrapper">
	{#snippet children()}
		<!-- Background Element -->
		<div class="philosophy-bg">
			<div class="philosophy-bg-grid"></div>
		</div>

		<div class="philosophy-content">
			<div class="philosophy-container">
				<!-- Headline -->
				<h2 class="philosophy-headline">
					{@html content.title}
				</h2>

				<!-- Subtitle -->
				<p class="philosophy-subtitle">
					{content.subtitle}
				</p>

				<!-- Text Blocks -->
				<div class="philosophy-blocks">
					{#each content.textBlocks as block, index}
						<div class="philosophy-text" style="animation-delay: {index * 0.1}s;">
							<p>{block.text}</p>
						</div>
					{/each}
				</div>
			</div>
		</div>
	{/snippet}
</GSAPSection>

<style>
	.philosophy-wrapper {
		background: var(--color-surface-elevated);
		position: relative;
	}

	.philosophy-bg {
		position: absolute;
		inset: 0;
		opacity: 0.1;
		pointer-events: none;
	}

	.philosophy-bg-grid {
		width: 100%;
		height: 100%;
		background-image: 
			linear-gradient(var(--color-accent) 1px, transparent 1px),
			linear-gradient(90deg, var(--color-accent) 1px, transparent 1px);
		background-size: 50px 50px;
		background-position: 0 0, 0 0;
		transform: rotate(15deg) scale(1.5);
		opacity: 0.1;
	}

	.philosophy-content {
		position: relative;
		z-index: 1;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 4rem 2rem;
	}

	.philosophy-container {
		max-width: 1000px;
		text-align: center;
	}

	.philosophy-headline {
		font-family: var(--font-heading);
		font-size: clamp(2.5rem, 6vw, 4rem);
		font-weight: 700;
		line-height: 1.1;
		letter-spacing: -0.03em;
		margin-bottom: 2rem;
		opacity: 0;
		transform: translateY(50px);
		clip-path: inset(100% 0 0 0);
	}

	.philosophy-subtitle {
		font-size: clamp(1.125rem, 2.5vw, 1.375rem);
		line-height: 1.6;
		color: var(--color-text-muted);
		margin-bottom: 4rem;
		opacity: 0;
		transform: translateY(30px);
	}

	.philosophy-blocks {
		display: grid;
		grid-template-columns: 1fr;
		gap: 2rem;
		max-width: 800px;
		margin: 0 auto;
	}

	.philosophy-text {
		opacity: 0;
		transform: translateY(30px);
	}

	.philosophy-text p {
		font-size: clamp(1rem, 2vw, 1.125rem);
		line-height: 1.7;
		color: var(--color-text);
		padding: 2rem;
		background: var(--color-surface);
		border-radius: var(--border-radius);
		box-shadow: var(--shadow-subtle);
		border-left: 4px solid var(--color-accent);
		position: relative;
	}

	.philosophy-text p::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(135deg, var(--color-accent) 0%, transparent 50%);
		opacity: 0.05;
		border-radius: var(--border-radius);
		pointer-events: none;
	}

	/* Responsive adjustments */
	@media (max-width: 768px) {
		.philosophy-content {
			padding: 3rem 1rem;
		}

		.philosophy-blocks {
			gap: 1.5rem;
		}

		.philosophy-text p {
			padding: 1.5rem;
		}
	}

	@media (min-width: 768px) {
		.philosophy-blocks {
			grid-template-columns: 1fr 1fr;
		}
	}

	/* Accent text styling */
	:global(.philosophy-headline .accent-text) {
		background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-secondary) 100%);
		-webkit-background-clip: text;
		background-clip: text;
		-webkit-text-fill-color: transparent;
		text-fill-color: transparent;
	}
</style>
