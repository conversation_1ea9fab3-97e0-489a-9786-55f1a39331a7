<script lang="ts">
	import { onMount } from 'svelte';
	import { GSAPAnimations } from '$lib/utils/gsap.js';
	import GSAPSection from '$lib/components/GSAPSection.svelte';
	import Button from '$lib/components/Button.svelte';

	interface Props {
		content: {
			title: string;
			subtitle: string;
			formFields: Array<{
				type: string;
				name: string;
				placeholder: string;
				required: boolean;
			}>;
			submitText: string;
			secondaryCTA: {
				text: string;
				link: string;
			};
		};
	}

	let { content }: Props = $props();
	let submitButton: HTMLElement;

	onMount(() => {
		// Add button wipe animation
		if (submitButton) {
			GSAPAnimations.buttonWipeAnimation(submitButton);
		}
	});

	function handleSubmit(event: Event) {
		event.preventDefault();
		// Handle form submission here
		console.log('Form submitted');
	}
</script>

<GSAPSection id="cta" animationType="cta" class="cta-wrapper">
	{#snippet children()}
		<!-- Background Elements -->
		<div class="cta-bg">
			<div class="cta-bg-element cta-bg-1"></div>
			<div class="cta-bg-element cta-bg-2"></div>
		</div>

		<div class="cta-content">
			<div class="cta-container">
				<!-- Headline -->
				<h2 class="cta-headline">
					{@html content.title}
				</h2>

				<!-- Subtitle -->
				<p class="cta-subtitle">
					{content.subtitle}
				</p>

				<!-- Contact Form -->
				<form class="cta-form" on:submit={handleSubmit}>
					{#each content.formFields as field}
						{#if field.type === 'textarea'}
							<textarea
								name={field.name}
								placeholder={field.placeholder}
								required={field.required}
								rows="4"
								class="form-input form-textarea"
							></textarea>
						{:else}
							<input
								type={field.type}
								name={field.name}
								placeholder={field.placeholder}
								required={field.required}
								class="form-input"
							/>
						{/if}
					{/each}

					<div class="form-actions">
						<button 
							type="submit" 
							class="cta-button submit-button"
							bind:this={submitButton}
						>
							<span class="button-text">{content.submitText}</span>
							<div class="button-wipe"></div>
						</button>

						<Button href={content.secondaryCTA.link} variant="outline" size="lg">
							{content.secondaryCTA.text}
						</Button>
					</div>
				</form>
			</div>
		</div>
	{/snippet}
</GSAPSection>

<style>
	.cta-wrapper {
		background: linear-gradient(135deg, var(--color-surface) 0%, var(--color-primary) 100%);
		position: relative;
	}

	.cta-bg {
		position: absolute;
		inset: 0;
		pointer-events: none;
		z-index: 0;
	}

	.cta-bg-element {
		position: absolute;
		border-radius: 50%;
		filter: blur(80px);
		opacity: 0.1;
	}

	.cta-bg-1 {
		top: 0;
		left: 25%;
		width: 400px;
		height: 400px;
		background: var(--color-accent);
		animation: float 10s ease-in-out infinite;
	}

	.cta-bg-2 {
		bottom: 0;
		right: 25%;
		width: 300px;
		height: 300px;
		background: var(--color-accent-secondary);
		animation: float 10s ease-in-out infinite reverse;
		animation-delay: -5s;
	}

	@keyframes float {
		0%, 100% {
			transform: translateY(0px) scale(1);
		}
		50% {
			transform: translateY(-30px) scale(1.1);
		}
	}

	.cta-content {
		position: relative;
		z-index: 1;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 4rem 2rem;
	}

	.cta-container {
		max-width: 800px;
		width: 100%;
		text-align: center;
	}

	.cta-headline {
		font-family: var(--font-heading);
		font-size: clamp(2.5rem, 6vw, 4rem);
		font-weight: 700;
		line-height: 1.1;
		letter-spacing: -0.03em;
		margin-bottom: 2rem;
		opacity: 0;
		transform: translateY(50px) scale(0.8);
	}

	.cta-subtitle {
		font-size: clamp(1.125rem, 2.5vw, 1.375rem);
		line-height: 1.6;
		color: var(--color-text-muted);
		margin-bottom: 3rem;
		opacity: 0;
		transform: translateY(30px);
	}

	.cta-form {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
		max-width: 600px;
		margin: 0 auto;
	}

	.form-input {
		padding: 1rem 1.5rem;
		border: 2px solid var(--color-border);
		border-radius: var(--border-radius);
		background: var(--color-surface);
		color: var(--color-text);
		font-size: 1rem;
		font-family: var(--font-body);
		transition: all 0.3s ease;
		opacity: 0;
		transform: translateY(30px);
	}

	.form-input:focus {
		outline: none;
		border-color: var(--color-accent);
		box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.1);
	}

	.form-input::placeholder {
		color: var(--color-text-muted);
	}

	.form-textarea {
		resize: vertical;
		min-height: 120px;
	}

	.form-actions {
		display: flex;
		flex-direction: column;
		gap: 1rem;
		margin-top: 1rem;
	}

	.submit-button {
		padding: 1rem 2rem;
		border: none;
		border-radius: var(--border-radius);
		background: var(--color-accent);
		color: var(--color-text);
		font-size: 1.125rem;
		font-weight: 600;
		font-family: var(--font-heading);
		cursor: pointer;
		position: relative;
		overflow: hidden;
		transition: all 0.3s ease;
		opacity: 0;
		transform: scale(0.8);
	}

	.submit-button:hover {
		transform: translateY(-2px);
		box-shadow: var(--shadow-large);
	}

	.button-text {
		position: relative;
		z-index: 2;
	}

	.button-wipe {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: var(--color-accent-secondary);
		transform: scaleX(0);
		transform-origin: left center;
		z-index: 1;
	}

	/* Responsive adjustments */
	@media (max-width: 768px) {
		.cta-content {
			padding: 3rem 1rem;
		}

		.form-actions {
			gap: 1.5rem;
		}
	}

	@media (min-width: 768px) {
		.form-actions {
			flex-direction: row;
			justify-content: center;
			gap: 2rem;
		}
	}

	/* Accent text styling */
	:global(.cta-headline .accent-text) {
		background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-secondary) 100%);
		-webkit-background-clip: text;
		background-clip: text;
		-webkit-text-fill-color: transparent;
		text-fill-color: transparent;
	}

	/* Reduced motion support */
	@media (prefers-reduced-motion: reduce) {
		.cta-bg-1,
		.cta-bg-2 {
			animation: none;
		}
	}
</style>
