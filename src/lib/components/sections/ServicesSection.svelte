<script lang="ts">
	import { onMount } from 'svelte';
	import { GSAPAnimations } from '$lib/utils/gsap.js';
	import GSAPSection from '$lib/components/GSAPSection.svelte';

	interface Props {
		content: {
			title: string;
			subtitle: string;
			items: Array<{
				icon: string;
				title: string;
				description: string;
			}>;
		};
	}

	let { content }: Props = $props();
	let serviceElements: HTMLElement[] = [];

	onMount(() => {
		// Add hover animations to service items
		serviceElements.forEach(element => {
			if (element) {
				GSAPAnimations.serviceHoverAnimation(element);
			}
		});
	});
</script>

<GSAPSection id="services" animationType="services" class="services-wrapper">
	{#snippet children()}
		<div class="services-content">
			<div class="services-container">
				<!-- Headline -->
				<h2 class="services-headline">
					{@html content.title}
				</h2>

				<!-- Subtitle -->
				<p class="services-subtitle">
					{content.subtitle}
				</p>

				<!-- Services Grid -->
				<div class="services-grid">
					{#each content.items as service, index}
						<div 
							class="service-item"
							bind:this={serviceElements[index]}
							style="animation-delay: {index * 0.15}s;"
						>
							<div class="service-icon">
								{service.icon}
							</div>
							<h3 class="service-title">
								{service.title}
							</h3>
							<p class="service-description">
								{service.description}
							</p>
						</div>
					{/each}
				</div>
			</div>
		</div>
	{/snippet}
</GSAPSection>

<style>
	.services-wrapper {
		background: var(--color-primary);
		position: relative;
	}

	.services-content {
		position: relative;
		z-index: 1;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 4rem 2rem;
	}

	.services-container {
		max-width: 1200px;
		text-align: center;
		width: 100%;
	}

	.services-headline {
		font-family: var(--font-heading);
		font-size: clamp(2.5rem, 6vw, 4rem);
		font-weight: 700;
		line-height: 1.1;
		letter-spacing: -0.03em;
		margin-bottom: 2rem;
		opacity: 0;
		transform: translateY(50px);
	}

	.services-subtitle {
		font-size: clamp(1.125rem, 2.5vw, 1.375rem);
		line-height: 1.6;
		color: var(--color-text-muted);
		margin-bottom: 4rem;
		max-width: 600px;
		margin-left: auto;
		margin-right: auto;
		opacity: 0;
		transform: translateY(30px);
	}

	.services-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
		gap: 2rem;
		margin-top: 3rem;
	}

	.service-item {
		background: var(--color-surface);
		border-radius: var(--border-radius-lg);
		padding: 3rem 2rem;
		box-shadow: var(--shadow-subtle);
		border: 1px solid var(--color-border);
		transition: all 0.3s ease;
		opacity: 0;
		transform: translateY(60px) scale(0.8);
		cursor: pointer;
		position: relative;
		overflow: hidden;
	}

	.service-item::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(135deg, var(--color-accent) 0%, transparent 50%);
		opacity: 0;
		transition: opacity 0.3s ease;
		pointer-events: none;
	}

	.service-item:hover::before {
		opacity: 0.05;
	}

	.service-item:hover {
		transform: translateY(-5px);
		box-shadow: var(--shadow-large);
		border-color: var(--color-accent);
	}

	.service-icon {
		font-size: 3rem;
		margin-bottom: 1.5rem;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 4rem;
		transition: transform 0.3s ease;
	}

	.service-title {
		font-family: var(--font-heading);
		font-size: 1.5rem;
		font-weight: 600;
		color: var(--color-accent);
		margin-bottom: 1rem;
		line-height: 1.3;
	}

	.service-description {
		color: var(--color-text-muted);
		line-height: 1.6;
		font-size: 1rem;
	}

	/* Responsive adjustments */
	@media (max-width: 768px) {
		.services-content {
			padding: 3rem 1rem;
		}

		.services-grid {
			grid-template-columns: 1fr;
			gap: 1.5rem;
		}

		.service-item {
			padding: 2rem 1.5rem;
		}
	}

	@media (min-width: 1024px) {
		.services-grid {
			grid-template-columns: repeat(4, 1fr);
		}
	}

	/* Accent text styling */
	:global(.services-headline .accent-text) {
		background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-secondary) 100%);
		-webkit-background-clip: text;
		background-clip: text;
		-webkit-text-fill-color: transparent;
		text-fill-color: transparent;
	}
</style>
