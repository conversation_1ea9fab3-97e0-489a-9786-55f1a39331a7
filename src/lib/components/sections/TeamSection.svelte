<script lang="ts">
	import GSAPSection from '$lib/components/GSAPSection.svelte';

	interface Props {
		content: {
			title: string;
			subtitle: string;
			members: Array<{
				name: string;
				title: string;
				description: string;
				stats: { number: number; suffix: string; label: string };
			}>;
		};
	}

	let { content }: Props = $props();
</script>

<GSAPSection id="team" animationType="team" class="team-wrapper">
	{#snippet children()}
		<div class="team-content">
			<div class="team-container">
				<!-- Section Header -->
				<div class="team-header">
					<h2 class="team-headline">
						{content.title}
					</h2>
					<p class="team-subtitle">
						{content.subtitle}
					</p>
				</div>

				<!-- Team Members (for pinned animation) -->
				<div class="team-members">
					{#each content.members as member, index}
						<div class="team-member" data-member={index}>
							<div class="member-content">
								<div class="member-info">
									<h3 class="member-name">{member.name}</h3>
									<p class="member-title">{member.title}</p>
									<p class="member-description">{member.description}</p>
								</div>
								
								<div class="member-stats">
									<div class="stat-number">
										{member.stats.number}<span class="stat-suffix">{member.stats.suffix}</span>
									</div>
									<div class="stat-label">{member.stats.label}</div>
								</div>
							</div>
						</div>
					{/each}
				</div>

				<!-- Progress Indicator -->
				<div class="team-progress">
					{#each content.members as _, index}
						<div class="progress-dot" data-dot={index}></div>
					{/each}
				</div>
			</div>
		</div>
	{/snippet}
</GSAPSection>

<style>
	.team-wrapper {
		background: var(--color-surface-elevated);
		position: relative;
		height: 100vh;
	}

	.team-content {
		position: relative;
		z-index: 1;
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 2rem;
	}

	.team-container {
		max-width: 1000px;
		width: 100%;
		text-align: center;
		position: relative;
	}

	.team-header {
		margin-bottom: 4rem;
	}

	.team-headline {
		font-family: var(--font-heading);
		font-size: clamp(2.5rem, 6vw, 4rem);
		font-weight: 700;
		line-height: 1.1;
		letter-spacing: -0.03em;
		margin-bottom: 2rem;
		color: var(--color-text);
	}

	.team-subtitle {
		font-size: clamp(1.125rem, 2.5vw, 1.375rem);
		line-height: 1.6;
		color: var(--color-text-muted);
		max-width: 600px;
		margin: 0 auto;
	}

	.team-members {
		position: relative;
		height: 400px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.team-member {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		opacity: 0;
		transform: translateY(50px);
	}

	.member-content {
		background: var(--color-surface);
		border-radius: var(--border-radius-lg);
		padding: 3rem;
		box-shadow: var(--shadow-large);
		border: 1px solid var(--color-border);
		max-width: 600px;
		width: 100%;
		position: relative;
		overflow: hidden;
	}

	.member-content::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 4px;
		background: linear-gradient(90deg, var(--color-accent) 0%, var(--color-accent-secondary) 100%);
	}

	.member-info {
		margin-bottom: 2rem;
	}

	.member-name {
		font-family: var(--font-heading);
		font-size: 2rem;
		font-weight: 700;
		color: var(--color-text);
		margin-bottom: 0.5rem;
		line-height: 1.2;
	}

	.member-title {
		font-size: 1.25rem;
		color: var(--color-accent);
		font-weight: 600;
		margin-bottom: 1rem;
	}

	.member-description {
		font-size: 1.125rem;
		line-height: 1.6;
		color: var(--color-text-muted);
	}

	.member-stats {
		text-align: center;
		padding-top: 2rem;
		border-top: 1px solid var(--color-border);
	}

	.stat-number {
		font-family: var(--font-heading);
		font-size: 3rem;
		font-weight: 800;
		color: var(--color-accent);
		line-height: 1;
		margin-bottom: 0.5rem;
	}

	.stat-suffix {
		font-size: 2rem;
		opacity: 0.8;
	}

	.stat-label {
		font-size: 1rem;
		color: var(--color-text-muted);
		font-weight: 500;
		text-transform: uppercase;
		letter-spacing: 0.05em;
	}

	.team-progress {
		display: flex;
		justify-content: center;
		gap: 1rem;
		margin-top: 3rem;
	}

	.progress-dot {
		width: 12px;
		height: 12px;
		border-radius: 50%;
		background: var(--color-border);
		transition: all 0.3s ease;
		cursor: pointer;
	}

	.progress-dot.active {
		background: var(--color-accent);
		transform: scale(1.2);
	}

	/* Responsive adjustments */
	@media (max-width: 768px) {
		.team-content {
			padding: 1rem;
		}

		.team-header {
			margin-bottom: 2rem;
		}

		.team-members {
			height: 350px;
		}

		.member-content {
			padding: 2rem;
		}

		.member-name {
			font-size: 1.75rem;
		}

		.stat-number {
			font-size: 2.5rem;
		}

		.stat-suffix {
			font-size: 1.5rem;
		}
	}

	/* Accent text styling */
	:global(.team-headline .accent-text) {
		background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-secondary) 100%);
		-webkit-background-clip: text;
		background-clip: text;
		-webkit-text-fill-color: transparent;
		text-fill-color: transparent;
	}
</style>
