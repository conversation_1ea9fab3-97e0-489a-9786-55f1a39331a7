<script lang="ts">
	import { onMount } from 'svelte';
	import { GSAPAnimations } from '$lib/utils/gsap.js';
	import Z<PERSON>ogo from '$lib/components/ZLogo.svelte';
	import Button from '$lib/components/Button.svelte';

	interface Props {
		content: {
			title: { line1: string; line2: string };
			subtitle: string;
			ctaText: string;
			ctaLink: string;
		};
	}

	let { content }: Props = $props();

	onMount(() => {
		// Initialize GSAP and run hero animation
		GSAPAnimations.init();
		
		// Small delay to ensure DOM is ready
		setTimeout(() => {
			GSAPAnimations.heroInitialAnimation();
		}, 100);
	});
</script>

<section class="hero-section" id="hero">
	<!-- Background Elements -->
	<div class="hero-bg">
		<div class="hero-bg-element hero-bg-1"></div>
		<div class="hero-bg-element hero-bg-2"></div>
	</div>

	<div class="hero-content">
		<!-- Logo -->
		<div class="hero-logo">
			<ZLogo size="xl" animated={true} />
		</div>

		<!-- Main Headline -->
		<h1 class="hero-headline">
			<span class="hero-word">{content.title.line1}</span>
			<span class="hero-word accent-text">{content.title.line2}</span>
		</h1>

		<!-- Subtitle -->
		<p class="hero-subtitle">
			{content.subtitle}
		</p>

		<!-- CTA Button -->
		<div class="hero-cta">
			<Button href={content.ctaLink} variant="primary" size="lg">
				{content.ctaText}
			</Button>
		</div>
	</div>
</section>

<style>
	.hero-section {
		height: 100vh;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		overflow: hidden;
		background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-surface) 100%);
	}

	.hero-bg {
		position: absolute;
		inset: 0;
		pointer-events: none;
		z-index: 0;
	}

	.hero-bg-element {
		position: absolute;
		border-radius: 50%;
		filter: blur(60px);
		opacity: 0.1;
	}

	.hero-bg-1 {
		top: 25%;
		left: 25%;
		width: 300px;
		height: 300px;
		background: var(--color-accent);
		animation: float 8s ease-in-out infinite;
	}

	.hero-bg-2 {
		bottom: 25%;
		right: 25%;
		width: 400px;
		height: 400px;
		background: var(--color-accent-secondary);
		animation: float 8s ease-in-out infinite reverse;
		animation-delay: -4s;
	}

	@keyframes float {
		0%, 100% {
			transform: translateY(0px) scale(1);
		}
		50% {
			transform: translateY(-20px) scale(1.05);
		}
	}

	.hero-content {
		position: relative;
		z-index: 1;
		text-align: center;
		max-width: 1200px;
		padding: 0 2rem;
	}

	.hero-logo {
		margin-bottom: 3rem;
	}

	.hero-headline {
		font-family: var(--font-heading);
		font-size: clamp(3rem, 8vw, 6rem);
		font-weight: 800;
		line-height: 0.95;
		letter-spacing: -0.04em;
		margin-bottom: 2rem;
		text-align: center;
	}

	.hero-word {
		display: block;
		opacity: 0;
		transform: translateY(50px) rotateX(90deg);
	}

	.hero-subtitle {
		font-size: clamp(1.25rem, 3vw, 1.5rem);
		line-height: 1.6;
		color: var(--color-text-muted);
		margin-bottom: 3rem;
		max-width: 600px;
		margin-left: auto;
		margin-right: auto;
		opacity: 0;
		transform: translateY(30px);
	}

	.hero-cta {
		opacity: 0;
		transform: translateY(30px);
	}

	.accent-text {
		background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-secondary) 100%);
		-webkit-background-clip: text;
		background-clip: text;
		-webkit-text-fill-color: transparent;
		text-fill-color: transparent;
	}

	/* Responsive adjustments */
	@media (max-width: 768px) {
		.hero-content {
			padding: 0 1rem;
		}

		.hero-logo {
			margin-bottom: 2rem;
		}

		.hero-headline {
			margin-bottom: 1.5rem;
		}

		.hero-subtitle {
			margin-bottom: 2rem;
		}

		.hero-bg-1,
		.hero-bg-2 {
			width: 200px;
			height: 200px;
		}
	}

	/* Reduced motion support */
	@media (prefers-reduced-motion: reduce) {
		.hero-bg-1,
		.hero-bg-2 {
			animation: none;
		}
	}
</style>
